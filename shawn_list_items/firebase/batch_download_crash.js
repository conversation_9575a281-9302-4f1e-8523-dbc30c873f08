


async function scrollAndExtractIssues() {
  let lastCount = 0;

  for (let i = 0; i < 20; i++) {
    window.scrollTo(0, document.body.scrollHeight);
    await new Promise(r => setTimeout(r, 1000)); // 等待页面加载

    const links = document.querySelectorAll('a.fire-router-link-host');
    if (links.length === lastCount) break;
    lastCount = links.length;
  }

  const issueIds = Array.from(document.querySelectorAll('a.fire-router-link-host'))
    .map(a => a.getAttribute('href'))
    .filter(href => href && href.includes('/issues/'))
    .map(href => href.match(/issues\/([^?]+)/)?.[1])
    .filter(Boolean);

  const unique = [...new Set(issueIds)];
  console.log('共抓取 issues 数：', unique.length);
  console.log(unique);
}

scrollAndExtractIssues();


// 使用 XPath 获取元素
let element = document.evaluate(
  '//*[@id="mat-tab-group-1-content-0"]/div/div/c9s-stack-trace-format-toggle/mat-button-toggle-group/button/span[3]',
  document,
  null,
  XPathResult.FIRST_ORDERED_NODE_TYPE,
  null
).singleNodeValue;

// 点击该元素
if (element) {
  element.click();
} else {
  console.warn("找不到目标元素");
}



<td _ngcontent-ng-c1815381270="" mat-cell="" class="mat-mdc-cell mdc-data-table__cell cdk-cell issue-title cdk-column-issue mat-column-issue ng-star-inserted" role="gridcell"><a _ngcontent-ng-c1815381270="" draggable="false" class="fire-router-link-host link-wrapper" href="/project/fzfbtest/crashlytics/app/android:com.example.playlet/issues/fc49a4450424b5a662a75cfc1d07af56?time=last-seven-days" style="cursor: pointer;"><issue-caption-table-cell _ngcontent-ng-c1815381270="" data-test-id="issue-caption-table-cell" _nghost-ng-c1484158452=""><div _ngcontent-ng-c1484158452="" class="caption-table-cell ng-star-inserted"><c9s-issue-caption-metadata-row _ngcontent-ng-c1484158452="" data-test-id="metadataWrapper" _nghost-ng-c440328931=""><div _ngcontent-ng-c440328931="" class="row-wrapper"><div _ngcontent-ng-c440328931="" data-test-id="eventTypeWrapper" class="event-type-wrapper ng-star-inserted"><div _ngcontent-ng-c440328931="" mattooltipposition="above" class="mat-mdc-tooltip-trigger event-type-tooltip-target" aria-describedby="cdk-describedby-message-ng-1-18" cdk-describedby-host="ng-1"><mat-icon _ngcontent-ng-c440328931="" role="img" class="mat-icon notranslate non-fatal-icon google-symbols mat-ligature-font mat-icon-no-color" aria-hidden="true" data-mat-icon-type="font"> error </mat-icon><span _ngcontent-ng-c440328931="" class="event-type-text"> Non-fatal </span></div><!----><mat-divider _ngcontent-ng-c440328931="" role="separator" class="mat-divider mat-divider-vertical ng-star-inserted" aria-orientation="vertical"></mat-divider><!----></div><!----><div _ngcontent-ng-c440328931="" data-test-id="captionDataWrapper" class="caption-metadata-wrapper ng-star-inserted"><!----><!----><div _ngcontent-ng-c440328931="" data-test-id="blamedFileWrapper" mattooltipposition="above" class="mat-mdc-tooltip-trigger metadata-wrapper ng-star-inserted" aria-describedby="cdk-describedby-message-ng-1-19" cdk-describedby-host="ng-1"><mat-icon _ngcontent-ng-c440328931="" role="img" fontset="google-symbols" class="mat-icon notranslate metadata-icons google-symbols mat-ligature-font mat-icon-no-color" aria-hidden="true" data-mat-icon-type="font" data-mat-icon-namespace="google-symbols"> insert_drive_file </mat-icon><span _ngcontent-ng-c440328931="" class="copy-target">package:get/get_instance/src/get_instance.dart:306</span></div><!----><!----></div><!----><c9s-issue-tags _ngcontent-ng-c440328931="" data-test-id="signals" _nghost-ng-c3857413102="" aria-label="has an issue signal" class="ng-star-inserted"><div _ngcontent-ng-c3857413102="" class="c9s-issue-tags ng-star-inserted"><fire-chip _ngcontent-ng-c3857413102="" tabindex="0" class="signal-chip fire-popover-trigger is-regressed is-size__dense is-hairline ng-star-inserted" _nghost-ng-c15124938="" style="--__fire-chip-bg-color: var(--theme-color-bg); --__fire-chip-color: var(--theme-color-ink-2);" aria-describedby="fbc_1b" aria-owns="fbc_1b"><div _ngcontent-ng-c15124938="" data-fire-popup-overlay-trigger="" class="chip-content"><mat-icon _ngcontent-ng-c3857413102="" role="img" class="mat-icon notranslate google-symbols mat-ligature-font mat-icon-no-color" aria-hidden="true" data-mat-icon-type="font">undo</mat-icon><span _ngcontent-ng-c15124938="" class="content"> Regressed issue </span></div><!----></fire-chip><div class="cdk-visually-hidden" aria-hidden="true" tabindex="0" data-focusinliner="true"></div><!----><fire-cdk-popover _ngcontent-ng-c3857413102="" _nghost-ng-c2795444092="" class="ng-star-inserted"><!----></fire-cdk-popover><!----></div><!----><!----></c9s-issue-tags><!----></div></c9s-issue-caption-metadata-row><div _ngcontent-ng-c1484158452="" data-test-id="titleWrapper" class="title-wrapper"><span _ngcontent-ng-c1484158452="" class="copy-target">GetInstance.find</span></div><div _ngcontent-ng-c1484158452="" data-test-id="subtitleWrapper" class="subtitle-wrapper ng-star-inserted"><span _ngcontent-ng-c1484158452="" class="copy-target">io.flutter.plugins.firebase.crashlytics.FlutterError - "SingleRowRankController" not found. You need to call "Get.put(SingleRowRankController())" or "Get.lazyPut(()=&gt;SingleRowRankController())". Error thrown building SingleRowRankView(dirty, state:
_SingleRowRankViewState#413b9).</span></div><!----><div _ngcontent-ng-c1484158452="" class="sub-row"><div _ngcontent-ng-c1484158452="" class="sub-issue-count ng-star-inserted"><mat-icon _ngcontent-ng-c1484158452="" role="img" class="mat-icon notranslate sub-issue-count-icon google-symbols mat-ligature-font mat-icon-no-color" aria-hidden="true" data-mat-icon-type="font">arrow_split</mat-icon><span _ngcontent-ng-c1484158452="" class="ng-star-inserted"> 10+ </span><!----><!----> variants </div><!----></div></div><!----></issue-caption-table-cell><!----><div _ngcontent-ng-c1815381270="" class="mobile-stats show-at-mobile"><span _ngcontent-ng-c1815381270="">261 non-fatals</span>&nbsp; <span _ngcontent-ng-c1815381270="">40 users</span></div></a></td>