<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>List Viewer</title>

    <!-- 不需要额外的UUID库 -->

    <!-- Start: Inline Script Block -->
    <script>
        // --- Content from js/auto_ffff_list.js ---
        // Helper function to get Beijing Time (UTC+8) string in YYYY-MM-DD_HH.mm format
        function getBeijingTimeString() {
            const now = new Date();
            const beijingTime = new Date(now.getTime() + 8 * 60 * 60 * 1000);
            const year = beijingTime.getUTCFullYear();
            const month = (beijingTime.getUTCMonth() + 1).toString().padStart(2, '0');
            const day = beijingTime.getUTCDate().toString().padStart(2, '0');
            const hours = beijingTime.getUTCHours().toString().padStart(2, '0');
            const minutes = beijingTime.getUTCMinutes().toString().padStart(2, '0');
            return `${year}-${month}-${day}_${hours}.${minutes}`;
        }
        // const version = "version: 1.1.6+11";
        const version = "version: 1.2.0+12";
        const targetV = version.split("version: ")[1].split("+")[0]; // 解析出 1.1.3
        const targetB = version.split("+")[1]; // 解析出 9

        const ffffList = {
            name: 'ffff',
            generateRandomString() {
                const timestamp = Date.now();
                const random = Math.floor(Math.random() * 1000000);
                return `${timestamp}${random}`;
            },
            generateFileName(type, env) {
                const ext = type === 'apk' ? 'apk' : 'aab';
                const ts = getBeijingTimeString();
                const versionFolder = `${targetV}_${targetB}`;
                return `${env}\\${versionFolder}\\${ts}.${ext}`;
            },
            generateFbUrl() {
                const ts = Date.now();
                const c = `j${this.generateRandomString()}`;
                return `https://w2a.flareflow.tv/fb02.html?ff_adid=50550&language=en&timestamp=${ts}` +
                    "&utm_source=facebook&campaign_id=7771432141324&campaign_name=app-ff_channel-fb_invest-w2a_system-android_optimist-alice_date-0411_shortid-7386485_unlocktype-mix_paypoint-9_epprice-50_feesku-0_adswitch-0_other-us-gb-de-aeo-1" +
                    `&adset_id={{adset.id}}&ad_id={{ad.id}}&ad_name={{ad.name}}&fbclid=${c}`;
            },
            generateTtUrl() {
                const ts = Date.now();
                const c = `7777droid${this.generateRandomString()}`;
                return `https://w2a.flareflow.tv/tt02.html?&utm_id=flareflowtiktokpixel&ff_adid=50551&language=en&timestamp=${ts}` +
                    "&utm_source=tiktok&campaign_id=124124323324&campaign_name=app-ff_channel-tt_invest-w2a_system-all_optimist-alice_date-0411_shortid-7386485_unlocktype-mix_paypoint-13_epprice-55_feesku-auto_adswitch-0_other-qcj" +
                    `&aid=__AID__&cid=__CID__&ttclid=${c}`;
            },
            getList() {
                const buildCommands = {
                    apk: (env) => `if (-not (Test-Path 'C:\\Users\\<USER>\\OneDrive\\${env}\\${targetV}_${targetB}')) { New-Item -ItemType Directory -Path 'C:\\Users\\<USER>\\OneDrive\\${env}\\${targetV}_${targetB}' -Force }; flutter build apk --release --flavor prod ; Copy-Item -Path "build\\app\\outputs\\apk\\prod\\release\\app-prod-release.apk" -Destination "C:\\Users\\<USER>\\OneDrive\\${this.generateFileName('apk', env)}" -Force`,
                    aab: (env) => `if (-not (Test-Path 'C:\\Users\\<USER>\\OneDrive\\${env}\\${targetV}_${targetB}')) { New-Item -ItemType Directory -Path 'C:\\Users\\<USER>\\OneDrive\\${env}\\${targetV}_${targetB}' -Force }; flutter build appbundle --release --flavor prod ; Copy-Item -Path "build\\app\\outputs\\bundle\\prodRelease\\app-prod-release.aab" -Destination "C:\\Users\\<USER>\\OneDrive\\${this.generateFileName('aab', env)}" -Force`
                };

                return [
                    { title: "代码:pubspec.yaml 修改 " + version, code: version },
                    { title: "代码:灰度无流量 QA内测", code: `static const String baseUrl = "https://api-noflow.flareflow.tv/ffff";` },
                    { title: "代码:灰度有流量 GP小范围更新", code: `static const String baseUrl = "https://api-gray.flareflow.tv/ffff";` },
                    // 测试环境 APK 构建命令
                    { title: "打包:APK - 灰度无流量", code: buildCommands.apk('_android\\灰度无流量') },
                    { title: "打包:APK - 灰度", code: buildCommands.apk('_android\\灰度') +";" + buildCommands.aab('_android\\灰度') },
                    { title: "打包:APK - 正式", code: buildCommands.apk('_android\\正式') +";" + buildCommands.aab('_android\\正式') },

                    // 其他命令
                    { title: "Generate Facebook URL", code: this.generateFbUrl() },
                    { title: "Generate TikTok URL", code: this.generateTtUrl() }
                ];
            },
            urlList: [
                { title: "发布-打包机", url: "https://jumpserver.ffff.team/luna/?login_to=cccd61f6-d472-4246-8d12-da781ccd429d&login_account=32d00e3a-f227-431a-9eb0-8feb1ff342ff" },
                { title: "发布-jenkins hexingyuan 123123", url: "https://jenkins.ffff.team/job/ffff-client-aaron/" },
                { title: "需求文档 1.2", url: "https://scnd0fd9eth3.feishu.cn/wiki/PxuzwsxCWisGLOkC1QGcHbsynpd" },
                { title: "飞书多语言", url: "https://scnd0fd9eth3.feishu.cn/wiki/CLeTwR5LBiTVnUkhTioc9dIfned?sheet=KkqTIW" },
                { title: "日志正式 hexingyuan 123456", url: "https://kibana.ffff.team/app/discover#/?_g=(filters:!(),refreshInterval:(pause:!t,value:60000),time:(from:now-6h,to:now))&_a=(columns:!(),dataSource:(dataViewId:'5ecf4c71-6406-47c5-9a96-3cf2d1becd1e',type:dataView),filters:!(),interval:auto,query:(language:kuery,query:%22loginappevent%22),sort:!(!('@timestamp',desc)))" },
                { title: "日志测试 readonly", url: "https://kibana-test.ffff.team/app/home#/" },
                { title: "pub.dev 埋点", url: "https://pub.dev/packages/dlink_analytics/example" },
                { title: "pub.dev 归因", url: "https://pub.dev/packages/deeplink_dev" },
                { title: "yapi", url: "https://yapi.ffff.team/project/30" },
                { title: "jira", url: "https://jira.ffff.team/" },
                { title: "figma", url: "https://www.figma.com/design/Yvo4AQXq1tv5YAVxt2sTIL/FlareFlow-UI?node-id=2-3&p=f&t=gO5FPqsGbdBQP2FU-0" },
            ]
        };

        // --- Content from js/auto_git_list.js ---
        const gitList = {
            name: 'git',
            getList() {
                // Replace with Git commands and descriptions
                return [
                    { title: "查看分支", code: "git branch -v" },
                    { title: "拉取分支", code: "git fetch origin/dev/1.1" },
                    { title: "合并分支", code: "git stash && git fetch --all && git rebase origin/dev/1.1 && git stash apply" },
                    { title: "取消上次提交", code: "git reset --soft HEAD~1" },
                    { title: "合并n次提交", code: "git rebase -i HEAD~3" },
                    { title: "查看stash", code: "echo \"git stash list     # 查看所有stash\"" },
                    { title: "取消rebase", code: "echo \"git rebase --abort # 取消正在进行的rebase\"" }
                ];
            },
            urlList: [
                { title: "Git文档", url: "https://git-scm.com/doc" },
                { title: "Github", url: "https://github.com" }
            ]
        };

        const pythonList = {
            name: 'python',
            getList() {
                return [
                    { title: "创建虚拟环境", code: "python3 -m venv .venv" },
                    { title: "启用虚拟环境", code: "source .venv/bin/activate" },
                    { title: "生成 requimrents.txt", code: "pip freeze > requirements.txt" },
                    { title: "安装 requimrents 里面的内容", code: "pip install -r requirements.txt" }
                ];
            },
            urlList: [
                { title: "Python官方文档", url: "https://docs.python.org" },
                { title: "PyPI", url: "https://pypi.org" }
            ]
        };

        const macList = {
            name: 'mac',
            getList() {
                return [
                    { title: "光标移动", code: "ctrl + a 移动到行首\n" +
                        "ctrl + e 移动到行尾\n" +
                        "ctrl + f 光标前移\n" +
                        "ctrl + b 光标后移\n" +
                        "ctrl + option + b 光标后移一个单词\n" +
                        "ctrl + n 光标下移一行\n" +
                        "ctrl + p 光标上移一行" },
                    { title: "文本编辑", code: "ctrl + u 转换为大写\n" +
                        "ctrl + option + u 转换为小写\n" +
                        "ctrl + d 删除光标后字符\n" +
                        "ctrl + k 删除光标后所有文本\n" +
                        "ctrl + h 删除光标前字符\n" +
                        "ctrl + o 在光标后插入新行\n" +
                        "option + delete 删除光标前单词\n" +
                        "option + 向上箭头 两行交换" },
                ];
            },
            urlList: [
                { title: "Mac快捷键官方指南", url: "https://support.apple.com/zh-cn/HT201236" }
            ]
        };

        const iosList = {
            name: 'ios',
            getList() {
                return [
                    { title: "重新构建 iOS", code: "flutter clean\nflutter pub get\ncd ios\npod install" }
                ];
            },
            urlList: [
                { title: "iOS 开发文档", url: "https://developer.apple.com/documentation/" }
            ]
        };

        const adbList = {
            name: 'adb',
            getList() {
                return [
                    { title: "重启 ADB 服务器", code: "./android/adb_tools.sh restart_adb" },
                    { title: "重启 ADB (原始命令)", code: "adb kill-server\nadb start-server\nadb devices" },
                    { title: "向设备输入文本", code: "./android/adb_tools.sh paste_to_clipboard \"Hello World\"" },
                    { title: "输入文本 (原始命令)", code: "adb shell input text \"your text here\"" },
                    { title: "获取设备剪贴板", code: "./android/adb_tools.sh get_clipboard" },
                    { title: "获取剪贴板 (原始命令)", code: "adb shell cmd clipboard get-text" },
                    { title: "复制文件 (原始命令)", code: "adb push \"/absolute/path/to/file\" \"/sdcard/Download/\"" },
                    { title: "查看连接的设备", code: "adb devices" },
                    { title: "连接到设备", code: "adb connect *************:5555" },
                    { title: "断开设备连接", code: "adb disconnect" },
                    { title: "安装 APK", code: "adb install app.apk" },
                    { title: "卸载应用", code: "adb uninstall com.package.name" },
                    { title: "查看设备信息", code: "adb shell getprop ro.product.model" },
                    { title: "截屏", code: "adb shell screencap -p /sdcard/screenshot.png\nadb pull /sdcard/screenshot.png" },
                    { title: "录屏", code: "adb shell screenrecord /sdcard/demo.mp4" },
                    { title: "查看日志", code: "adb logcat" },
                    { title: "清除日志", code: "adb logcat -c" },
                    { title: "进入设备 Shell", code: "adb shell" },
                    { title: "查看 ADB 工具帮助", code: "./android/adb_tools.sh help" },
                    { title: "复制文件到设备下载目录", code: "./android/adb_tools.sh copy_to_download \"/absolute/path/to/file\"" },
                    { title: "复制文件到指定设备", code: "./android/adb_tools.sh copy_to_download \"/absolute/path/to/file\" -d emulator-5554" }
                ];
            },
            urlList: [
                { title: "ADB 官方文档", url: "https://developer.android.com/studio/command-line/adb" },
                { title: "Android 开发者文档", url: "https://developer.android.com/" },
                { title: "ADB 命令大全", url: "https://github.com/mzlogin/awesome-adb" }
            ]
        };

        // --- Combine and define Alpine component ---
        window.listModules = [ffffList, gitList, pythonList, macList, iosList, adbList];

        window.listViewer = function() {
            return {
                tabs: [],
                items: [],
                urls: [],
                currentTab: 'ffff',
                showStatus: false,
                statusMessage: '',

                init() {
                    this.initTabs();
                    this.loadList();
                },
                initTabs() {
                    if (window.listModules) { this.tabs = window.listModules.map(m => ({ id: m.name, name: m.name })); }
                    else { console.warn("window.listModules not found"); }
                },
                loadList() {
                    const currentModule = window.listModules.find(m => m.name === this.currentTab);
                    if (currentModule && typeof currentModule.getList === 'function') {
                        try {
                            this.items = currentModule.getList();
                            this.urls = currentModule.urlList || [];
                        }
                        catch (e) {
                            console.error("Error getting list:", e);
                            this.items = [];
                            this.urls = [];
                        }
                    } else {
                        this.items = [];
                        this.urls = [];
                    }
                },
                copyToClipboard(codeToCopy) { navigator.clipboard.writeText(codeToCopy).then(() => this.showStatusMessage('代码已复制')).catch(err => this.showStatusMessage('复制失败')); },
                showStatusMessage(msg) { this.statusMessage = msg; this.showStatus = true; setTimeout(() => this.showStatus = false, 2000); }
            }
        };
    </script>
    <!-- End: Inline Script Block -->

    <script defer src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js"></script>
    <style>
        /* 全局样式 */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: Helvetica, Arial, sans-serif;
        }

        body {
            background-color: #f0f2f5;
            color: #1c1e21;
            line-height: 1.34;
            padding: 16px;
        }

        /* 容器样式 */
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        /* 标签页样式 */
        .tabs {
            display: flex;
            width: 100%;
            background-color: #1877f2;
            border-bottom: 1px solid #dddfe2;
        }

        .tab-button {
            padding: 12px 16px;
            cursor: pointer;
            color: #fff;
            font-weight: 600;
            font-size: 15px;
            border: none;
            background: none;
            transition: background-color 0.3s;
            flex: 1;
            text-align: center;
            border-right: 1px solid rgba(255, 255, 255, 0.2);
        }

        .tab-button:last-child {
            border-right: none;
        }

        .tab-button:hover {
            background-color: rgba(255, 255, 255, 0.1);
        }

        .tab-button.active {
            background-color: #166fe5;
            box-shadow: inset 0 -3px 0 #fff;
        }

        /* 内容区域样式 */
        .content {
            display: flex;
            width: 100%;
            min-width: 0;
        }

        /* URL列表样式 */
        .url-list {
            width: 180px;
            flex-shrink: 0;
            padding: 12px;
            border-right: 1px solid #dddfe2;
            background-color: #f7f8fa;
            overflow-y: auto;
        }

        .url-item {
            display: block;
            margin-bottom: 8px;
            padding: 10px;
            text-decoration: none;
            color: #1877f2;
            font-size: 14px;
            border-radius: 6px;
            background-color: #fff;
            border: 1px solid #dddfe2;
            transition: all 0.3s;
            font-weight: 500;
            word-break: break-word;
        }

        .url-item:hover {
            background-color: #e7f3ff;
            box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
        }

        /* 代码列表样式 */
        .code-list {
            flex: 1;
            min-width: 0;
            padding: 12px;
            overflow-x: auto;
        }

        .code-item {
            margin-bottom: 12px;
            padding: 12px;
            background-color: #f7f8fa;
            border-radius: 6px;
            border: 1px solid #dddfe2;
            transition: all 0.2s;
        }

        .code-item:has(pre[x-show="item.code"]:not([hidden])) {
            cursor: pointer;
        }

        .code-item:has(pre[x-show="item.code"]:not([hidden])):hover {
            background-color: #e7f3ff;
            border-color: #1877f2;
        }

        .code-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: #3d4045;
            font-size: 14px;
        }

        pre {
            white-space: pre-wrap;
            word-wrap: break-word;
            background-color: #fff;
            padding: 8px;
            border-radius: 4px;
            border: 1px solid #dddfe2;
            font-size: 13px;
            color: #3d4045;
            line-height: 1.5;
        }

        code {
            font-family: Consolas, Monaco, "Courier New", monospace;
        }

        /* 状态提示样式 */
        .status-message {
            position: fixed;
            bottom: 20px;
            left: 50%;
            transform: translateX(-50%);
            background-color: #3b5998;
            color: white;
            padding: 10px 16px;
            border-radius: 6px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
            z-index: 1000;
            font-size: 14px;
            font-weight: 500;
        }
    </style>
</head>
<body>
    <div x-data="listViewer()" class="container">
        <!-- 标签页 -->
        <div class="tabs">
            <template x-for="(tab, index) in tabs" :key="index">
                <button
                    class="tab-button"
                    :class="{'active': currentTab === tab.id}"
                    x-text="tab.name"
                    @click="currentTab = tab.id; loadList()"
                ></button>
            </template>
        </div>

        <!-- 内容区域（左侧URL列表 + 右侧代码列表）-->
        <div class="content">
            <!-- 左侧URL列表（固定宽度180px）-->
            <div class="url-list">
                <template x-for="(url, index) in urls" :key="index">
                    <a :href="url.url" target="_blank" class="url-item" x-text="url.title"></a>
                </template>
            </div>

            <!-- 右侧代码列表 -->
            <div class="code-list">
                <template x-for="(item, index) in items" :key="index">
                    <div @click="item.code ? copyToClipboard(item.code) : null" class="code-item">
                        <div x-show="item.title" class="code-title" x-text="item.title"></div>
                        <pre x-show="item.code"><code x-text="item.code"></code></pre>
                    </div>
                </template>
            </div>
        </div>

        <!-- 状态提示 -->
        <div
            x-show="showStatus"
            x-transition
            class="status-message"
            x-text="statusMessage"
        ></div>
    </div>
</body>
</html>