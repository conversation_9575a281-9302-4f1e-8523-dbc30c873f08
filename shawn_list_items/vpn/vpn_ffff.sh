# 关闭 OpenVPN 和 ClashX
echo "正在关闭 OpenVPN 和 ClashX..."
# 获取 OpenVPN 的所有进程 PID
PIDS=$(pgrep -f "OpenVPN Connect|ClashX")

# 如果找到了 PID，逐个终止进程
if [ -n "$PIDS" ]; then
    for PID in $PIDS; do
        kill "$PID"
        echo "已终止: PID $PID"
    done
else
    echo "OpenVPN 和 ClashX 未运行"
fi

# 设置 VPN 服务名称
VPN_SERVICE="ffff"

# 获取 VPN 的连接状态
VPN_STATUS=$(networksetup -showpppoestatus "$VPN_SERVICE")
echo "VPN 状态: $VPN_STATUS"
# 检查是否处于连接状态
if [[ "$VPN_STATUS" == "connected" ]]; then
    # 如果 VPN 已连接，则断开连接
    echo "VPN 已连接"
else
    # 关闭 OpenVPN 和 ClashX
    echo "正在关闭 OpenVPN 和 ClashX..."
    # 获取 OpenVPN 的所有进程 PID
    PIDS=$(pgrep -f "OpenVPN Connect|ClashX")

    # 如果找到了 PID，逐个终止进程
    if [ -n "$PIDS" ]; then
        for PID in $PIDS; do
            kill "$PID"
            echo "OpenVPN 已终止: PID $PID"
        done
    else
        echo "OpenVPN 未运行"
    fi

    # 如果 VPN 未连接，则启动连接
    echo "VPN 未连接，正在启动连接..."
    networksetup -connectpppoeservice "$VPN_SERVICE"
    sleep 8
    echo "VPN 已连接"
fi