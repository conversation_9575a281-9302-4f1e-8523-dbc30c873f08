#!/bin/bash
cd /Users/<USER>/Projects/events-verification/

# Set the current branch to prod_dev_zyr_1.1
git checkout prod_dev_zyr_1.1 

# Fetch the latest changes from both remotes
git fetch origin
git fetch kalateam 
echo "Fetch completed successfully."
# Merge origin/dev_zyr_1.1 into prod_dev_zyr_1.1
git merge origin/dev_zyr_1.1 
echo "Merge origin/dev_zyr_1.1 completed successfully."
# Merge kalateam/dev_zyr_1.1 into prod_dev_zyr_1.1
git merge kalateam/dev_zyr_1.1
echo "Merge completed successfully."
git push kalateam prod_dev_zyr_1.1:dev_zyr_1.1
echo "Merge and push completed successfully."