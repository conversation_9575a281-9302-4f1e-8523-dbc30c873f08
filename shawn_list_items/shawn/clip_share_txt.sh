#!/bin/bash

# Step 1: 获取剪贴板文本
clipboard_text=$(pbpaste)

# 检查剪贴板内容是否为空或不是文本
if [[ -z "$clipboard_text" ]]; then
  # 使用 AppleScript 弹出系统通知
  osascript -e 'display notification "剪贴板没有文本内容或文本为空！" with title "提示"'
  exit 1
fi

# Step 2: 在 Downloads/txt 目录下创建一个带时间戳的 .txt 文件
timestamp=$(date +"%Y%m%d_%H%M%S")
file_path="$HOME/Downloads/txt/$timestamp.txt"

# 创建目录（如果不存在的话）
mkdir -p "$HOME/Downloads/txt"

# Step 3: 将剪贴板文本写入该文件
echo "$clipboard_text" > "$file_path"

# Step 4: 使用 AppleScript 在 Finder 中显示该文件
osascript -e "
tell application \"Finder\"
    activate
    reveal POSIX file \"$file_path\"
end tell
"

# 提示用户文件已创建
osascript -e "display notification \"文件已创建并保存为: $file_path\" with title \"提示\""