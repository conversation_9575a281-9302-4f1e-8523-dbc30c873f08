import re
import os
import shutil

def extract_issue_from_text(text):
    """
    从文本中提取 Issue 值。

    Args:
        text (str): 包含 Issue 的文本。

    Returns:
        str or None: 提取到的 Issue 值，如果没有找到则返回 None。
    """
    issue_match = re.search(r"Issue: ([0-9a-fA-F]+)", text)
    if issue_match:
        return issue_match.group(1)
    return None

def move_fixed_files(root_dir, issue_list):
    """
    将 ffff/.crash/1.2.0 中文件名包含 issue 的文件移动到 ffff/.crash/1.2.0_fixed。

    Args:
        root_dir (str): 根目录路径。
        issue_list (list): 包含已解决的 issue 值的列表。
    """
    source_dir = os.path.join(root_dir, ".crash", "1.2.0")
    fixed_dir = os.path.join(root_dir, ".crash", "1.2.0_fixed")

    os.makedirs(fixed_dir, exist_ok=True)

    if not os.path.exists(source_dir):
        print(f"Source directory '{source_dir}' does not exist.")
        return

    moved_count = 0
    for filename in os.listdir(source_dir):
        filepath = os.path.join(source_dir, filename)
        if os.path.isfile(filepath):
            for issue in issue_list:
                if issue in filename:
                    destination_path = os.path.join(fixed_dir, filename)
                    try:
                        shutil.move(filepath, destination_path)
                        print(f"Moved '{filename}' to '{fixed_dir}' because it contains issue '{issue}'.")
                        moved_count += 1
                        break  # 找到匹配的 issue 后停止对当前文件的检查
                    except Exception as e:
                        print(f"Error moving '{filename}' to '{fixed_dir}': {e}")

    print(f"Moved {moved_count} files to '{fixed_dir}'.")

if __name__ == "__main__":
    root_directory = "ffff"  # 请将 "ffff" 替换为实际的根目录路径
    fix_history_file = os.path.join(root_directory, "4_feishu_fix_history.txt")
    fixed_text = ""

    try:
        with open(fix_history_file, 'r', encoding='utf-8') as f:
            fixed_text = f.read()
    except FileNotFoundError:
        print(f"Error: File '{fix_history_file}' not found.")
    except Exception as e:
        print(f"Error reading file '{fix_history_file}': {e}")

    extracted_issues = []
    if fixed_text:
        for item in fixed_text.strip().split('\n\n'):  # 以两个换行符分割文本块
            issue = extract_issue_from_text(item)
            if issue:
                extracted_issues.append(issue)

    print("Extracted Issues from fix_history.txt:", extracted_issues)

    move_fixed_files(root_directory, extracted_issues)