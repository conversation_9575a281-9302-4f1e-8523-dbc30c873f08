#!/bin/bash

# 设置变量
AVD_NAME="Pixel_6"
VPN_SCRIPT="/Users/<USER>/Projects/python/shawn_list_items/vpn/vpn_clash_x.sh"
EMULATOR_PATH="/Users/<USER>/Library/Android/sdk/emulator/emulator"

# 可选：启动 VPN（如果你始终想要 VPN）
# echo "[INFO] Starting VPN..."
# $VPN_SCRIPT
# sleep 3

# 检查模拟器是否已启动
EMULATOR_PID=$(pgrep -f "emulator.*-avd $AVD_NAME")
if [ -n "$EMULATOR_PID" ]; then
  echo "[INFO] Emulator $AVD_NAME is already running. Stopping it..."
  adb -s emulator-5554 emu kill
  sleep 3
fi

# 启动模拟器（尝试 QEMU 自定义网络）
echo "[INFO] Launching emulator $AVD_NAME with QEMU network settings..."
"$EMULATOR_PATH" -avd "$AVD_NAME" \
  -gpu host \
  -no-snapshot-load \
  -no-boot-anim \
  -audio stereo \
  -netdelay none \
  -netspeed full \
  -qemu -net nic -net user,hostfwd=tcp::5555-:5555