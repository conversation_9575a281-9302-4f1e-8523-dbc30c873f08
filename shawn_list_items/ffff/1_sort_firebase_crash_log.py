import os
import re
import shutil
import hashlib

def parse_crash_file(filepath):
    """
    解析 crash 文件，提取 Platform 和 Issue。

    Args:
        filepath (str): crash 文件路径。

    Returns:
        tuple: (platform, issue)，如果无法解析则返回 (None, None)。
    """
    try:
        with open(filepath, 'r', encoding='utf-8') as f:
            content = f.read()
            platform_match = re.search(r"# Platform: (.+)", content)
            issue_match = re.search(r"# Issue: (.+)", content)
            if platform_match and issue_match:
                platform = platform_match.group(1).strip()
                issue = issue_match.group(1).strip()
                return platform, issue
            else:
                return None, None
    except Exception as e:
        print(f"Error reading file {filepath}: {e}")
        return None, None

def generate_new_filename(platform, issue):
    """
    生成新的文件名。
    根据你的要求，文件名格式应为 "平台_Issue值.log"。

    Args:
        platform (str): 平台名称。
        issue (str): Issue 值。

    Returns:
        str: 新的文件名。
    """
    # 直接使用 Issue 值，而不是它的哈希值
    return f"{platform.lower()}_{issue}.log"

def process_crash_files(root_dir):
    """
    处理指定目录下的所有 crash 文件。

    Args:
        root_dir (str): 包含 crash 文件的根目录。
    """
    source_dir = os.path.join(root_dir, ".crash", "1.2.0")
    fixed_dir = os.path.join(root_dir, ".crash", "1.2.0_fixed")
    unknown_dir = os.path.join(root_dir, ".crash", "1.2.0_unknown")

    os.makedirs(fixed_dir, exist_ok=True)
    os.makedirs(unknown_dir, exist_ok=True)

    processed_filenames = set() # 用于跟踪已处理的文件名，实现去重

    if not os.path.exists(source_dir):
        print(f"Source directory '{source_dir}' does not exist.")
        return

    for filename in os.listdir(source_dir):
        filepath = os.path.join(source_dir, filename)
        if os.path.isfile(filepath): # 确保是文件而不是目录
            # 1. 如果文件已经在 ffff/.crash/1.2.0_fixed 里, 则直接删除文件, 并且打印日志
            # 注意：这里是检查原始文件名是否在 fixed 目录中，如果 fixed 目录中的文件已经被重命名，
            # 那么这个检查可能需要调整。但根据描述，是“如果文件已经在 fixed 里”，所以按原始文件名检查。
            fixed_filepath_check = os.path.join(fixed_dir, filename)
            if os.path.exists(fixed_filepath_check):
                os.remove(filepath)
                print(f"File '{filename}' already exists in '{fixed_dir}'. Deleted original file from source.")
                continue # 处理下一个文件

            platform, issue = parse_crash_file(filepath)

            if platform and issue:
                new_filename = generate_new_filename(platform, issue)
                new_filepath = os.path.join(source_dir, new_filename) # 暂时重命名到源目录

                # 2. 将文件名改成 例如 android_f4df6d7e7694bdf8d557967cfa98d234.log,
                #    并且删掉原始文件, 如果文件名重复, 直接删除文件,实现去重
                if new_filename in processed_filenames:
                    os.remove(filepath) # 删除原始文件
                    print(f"Duplicate filename '{new_filename}' detected based on Issue. Deleted original file '{filename}'.")
                else:
                    try:
                        # 检查新文件名是否已经存在于源目录中（可能是之前处理过的文件）
                        if os.path.exists(new_filepath) and filepath != new_filepath:
                            os.remove(filepath) # 删除原始文件
                            print(f"Target filename '{new_filename}' already exists in source. Deleted original file '{filename}'.")
                        else:
                            os.rename(filepath, new_filepath)
                            processed_filenames.add(new_filename)
                            print(f"Renamed '{filename}' to '{new_filename}'.")
                    except OSError as e:
                        print(f"Error renaming '{filename}' to '{new_filename}': {e}")
                        # 如果重命名失败，将文件视为无法处理，移动到 unknown
                        unknown_filepath = os.path.join(unknown_dir, filename)
                        shutil.move(filepath, unknown_filepath)
                        print(f"Failed to rename '{filename}'. Moved to '{unknown_dir}'.")
            else:
                # 4. 如果遇到无法解析出 Platform 的值, 以及 Issue的值,
                #    则将文件移到 ffff/.crash/1.2.0_unknown 里
                unknown_filepath = os.path.join(unknown_dir, filename)
                try:
                    shutil.move(filepath, unknown_filepath)
                    print(f"Could not parse Platform or Issue from '{filename}'. Moved to '{unknown_dir}'.")
                except Exception as e:
                    print(f"Error moving '{filename}' to '{unknown_dir}': {e}")


if __name__ == "__main__":
    root_directory = "ffff"  # 请将 "ffff" 替换为实际的根目录路径
    process_crash_files(root_directory)