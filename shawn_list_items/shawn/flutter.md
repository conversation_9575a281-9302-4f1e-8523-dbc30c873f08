如果你是 flutter 移动端高级工程师 你需要构建一个适合 ai 辅助编程的整体框架 , 1 需要在 android ios web 上运行 , web 2 需要有数据库 3 需要有持久化存储(例如 ios android 用 mmkv web 用 随便, 后续支持方便替换) 4 mvi 架构 5 需要有全局对话框管理, 例如同时多个对话框弹出 6 需要统一的广告逻辑模块 (banner 激励, 插屏, 原生模板) 7 要用最新的 flutter 版本 8 要用适合 mvi ai 辅助编程的架构, 9 要有好用的路由导航 10 要从设计上避免内存泄露! 11 要有 dev 与 prod 区别, 12 dev要用强大的日志库, 所有的日志都要写入专门的日志数据, app 端

请问可以用什么技术方案? 项目架构
lib/
├── main.dart                      // 启动入口
├── app.dart                       // App 初始化 & 环境注入
├── core/                          // 核心功能模块（不可依赖 UI）
│   ├── config/                    // 环境配置 & 常量
│   ├── logger/                    // 日志系统（开发用）
│   ├── analytics/                // 埋点系统（事件上报 + 数据库）
│   ├── storage/                   // 本地存储抽象（支持 MMKV / IndexedDB）
│   ├── ads/                       // 广告模块封装
│   ├── platform/                  // 原生通道封装
├── data/                          // 数据层（DB / Repository）
│   ├── models/                    // 数据模型定义
│   ├── db/                        // Drift / SQLite 数据库
│   ├── repositories/             // Repository 抽象与实现
├── features/                      // 按模块划分的功能区（MVI）
│   └── counter/                  
│       ├── intent.dart
│       ├── controller.dart
│       ├── state.dart
│       └── view/                 // UI Widgets
├── router/                        // go_router 定义
├── services/                      // 全局服务（导航 / 对话框等）
│   ├── dialog_service.dart
│   └── navigation_service.dart当然，Shawn。以下是根据你所有需求（包括 MVI 架构、日志系统、埋点系统、平台兼容、AI 辅助开发等）整理出的 Flutter 架构设计文档（AI 辅助开发友好版）。

⸻

📘 Flutter 项目架构设计文档（支持 AI 辅助开发）

适用于构建 Android / iOS / Web 通用 Flutter 应用，集成现代 MVI 架构，具备日志、埋点、广告、路由、状态管理、平台抽象等完整系统，并为 AI 辅助编程优化代码结构和模块组织。

⸻

📐 架构总览

lib/
├── main.dart                      // 启动入口
├── app.dart                       // App 初始化 & 环境注入
├── core/                          // 核心功能模块（不可依赖 UI）
│   ├── config/                    // 环境配置 & 常量
│   ├── logger/                    // 日志系统（开发用）
│   ├── analytics/                // 埋点系统（事件上报 + 数据库）
│   ├── storage/                   // 本地存储抽象（支持 MMKV / IndexedDB）
│   ├── ads/                       // 广告模块封装
│   ├── platform/                  // 原生通道封装
├── data/                          // 数据层（DB / Repository）
│   ├── models/                    // 数据模型定义
│   ├── db/                        // Drift / SQLite 数据库
│   ├── repositories/             // Repository 抽象与实现
├── features/                      // 按模块划分的功能区（MVI）
│   └── counter/                  
│       ├── intent.dart
│       ├── controller.dart
│       ├── state.dart
│       └── view/                 // UI Widgets
├── router/                        // go_router 定义
├── services/                      // 全局服务（导航 / 对话框等）
│   ├── dialog_service.dart
│   └── navigation_service.dart


⸻

🧠 架构核心原则（AI 辅助优化）

模块	原则
MVI 架构	Intent → Controller（Model）→ State → View，逻辑解耦、函数式结构适合 AI 理解与生成
模块化功能结构	每个 feature 拆为单独目录，方便 AI 理解上下文
统一常量管理	所有字符串常量集中在 core/config/constants.dart
抽象服务接口	所有原生 / 平台 / 插件服务都封装为接口，便于替换与 AI 自动调用
代码注释清晰	所有核心接口与结构加上 doc 注释，利于 AI 分析


⸻

🧾 日志系统（开发环境专用）

功能说明：
	•	仅在 dev 环境启用
	•	所有日志通过统一 Logger 接口调用
	•	同步写入本地日志数据库（log.db）

接口：

abstract class IAppLogger {
  void d(String message, {Map<String, dynamic>? extra});
  void e(String message, {Object? error, StackTrace? stack});
  Future<void> flush();
}


⸻

📊 埋点系统（Analytics）

功能目标：

要求	描述
事件上报	每个事件包含 eventKey 和 {propKey: propValue}
常量统一管理	所有 eventKey 与 propKey 字符串统一定义在常量类
本地数据库存储	独立埋点数据库（analytics.db），用于调试、上传与恢复机制
与 MVI 整合	支持在 intent → controller 中快速埋点封装调用
可接入外部 SDK	后期可接入 Firebase / 自研埋点平台等

示例 API：

abstract class IAnalyticsService {
  Future<void> trackEvent(String eventKey, Map<String, dynamic> props);
  Future<void> flush(); // 可手动触发上传或清理
}

常量管理建议：

class AnalyticsEvents {
  static const login = 'login_event';
  static const purchase = 'purchase_event';
}

class AnalyticsProps {
  static const userId = 'user_id';
  static const productId = 'product_id';
}


⸻

🧩 MVI 架构规范（AI 友好）

每个模块目录结构如下：

features/feature_name/
├── intent.dart           // 用户意图定义（例如点击、输入）
├── state.dart            // UI 状态（immutable）
├── controller.dart       // intent → state 逻辑处理
├── view/                 // UI Widget 构建

示例结构：

// intent.dart
sealed class CounterIntent {
  const CounterIntent();
  factory CounterIntent.increment() = _Increment;
}

// controller.dart
class CounterController extends StateNotifier<CounterState> {
  void onIntent(CounterIntent intent) {
    if (intent is _Increment) {
      state = state.copyWith(count: state.count + 1);
      analytics.trackEvent(AnalyticsEvents.increment, {
        AnalyticsProps.userId: currentUser.id,
      });
    }
  }
}


⸻

📺 广告模块

支持类型：
	•	Banner
	•	插屏
	•	激励视频
	•	原生模板广告

抽象接口：

abstract class IAdService {
  Future<void> loadAd(AdType type);
  Future<void> showAd(AdType type);
}

enum AdType { banner, interstitial, rewarded, native }


⸻

🧭 路由与导航系统
	•	使用 go_router
	•	支持嵌套路由、守卫、参数解析
	•	可结合 AI 自动生成路由表

示例：

final router = GoRouter(
  routes: [
    GoRoute(path: '/', builder: (ctx, state) => HomePage()),
    GoRoute(path: '/login', builder: (ctx, state) => LoginPage()),
  ],
);


⸻

🌍 Dev / Prod 环境区分
	•	使用 Flavor 架构 or --dart-define=env=dev
	•	配置文件统一抽象 EnvironmentConfig，支持多环境注入
	•	日志 & 埋点行为在 Dev 与 Prod 中可自由开关

⸻

💾 本地持久化（KV & DB）

平台	使用方案	封装建议
Android/iOS	MMKV / SharedPreferences	IKeyValueStorage 接口封装
Web	IndexedDB / Hive	通过 StorageAdapter 抽象统一接口
数据库	Drift（推荐） / Isar	写入日志与埋点数据分别管理


⸻

🧠 AI 辅助开发提示建议
	•	所有类、方法使用 DartDoc 注释
	•	状态管理使用 Riverpod（推荐）或 freezed，利于自动生成
	•	所有事件、key、props 尽量使用统一常量，便于 AI 自动补全与修改
	•	控制器中的意图处理逻辑函数要清晰命名，并可抽出到 mixin 模块中

⸻

是否需要我生成：
	1.	analytics.db 埋点数据库结构示例（使用 Drift）
	2.	一份完整的 analytics_service.dart 实现
	3.	与 MVI 集成的埋点触发模板？

只需告诉我「要生成哪个」，我可以继续补充。