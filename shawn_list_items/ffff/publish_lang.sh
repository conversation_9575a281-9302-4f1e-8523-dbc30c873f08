/Users/<USER>/Projects/python/shawn_list_items/vpn/vpn_ffff.sh

#!/bin/zsh

LOG_FILE="/Users/<USER>/Projects/python/shawn_list_items/ffff/.cache/publish_lang.log"
export PATH="/Users/<USER>/flutter/bin:$PATH"
# 1. 删除旧日志文件
rm -f "$LOG_FILE"

# 2. 将所有输出重定向到日志文件
exec > >(tee -a "$LOG_FILE") 2>&1

echo "当前 PATH 是：$PATH"
echo "脚本开始执行：$(date)"

# 进入目标目录
cd /Users/<USER>/Projects/projectSamples/arron_i18n || { echo "进入目录失败"; exit 1; }

# 执行 git stash
echo "执行 git stash..."
git stash --include-untracked || { echo "git stash 执行失败"; exit 1; }

# 执行 git fetch
echo "执行 git fetch --all..."
git fetch --all

# 执行 git rebase
echo "执行 rebase origin/dev/1.2..."
git rebase origin/dev/1.2 || { echo "git rebase 执行失败"; exit 1; }

# 执行 Python 脚本
echo "执行 Python 脚本..."
cd /Users/<USER>/Projects/python/ffff_multi_language
venv/bin/python trans_feishu.py || { echo "Python 脚本执行失败"; exit 1; }

# 执行 Flutter 构建
echo "执行 Flutter 构建..."
cd /Users/<USER>/Projects/projectSamples/arron_i18n
flutter clean
flutter pub get
# flutter build apk --debug --flavor dev || { echo "Flutter 构建失败"; exit 1; }
flutter build bundle || { echo "构建校验失败"; exit 1; }

# 如果构建成功，执行 git commit
echo "执行 git commit..."
git add .

echo "以下是改动的文件及其内容："
git diff --cached

git commit -m "feat:多语言素材更新" || { echo "git commit 执行失败"; exit 1; }

# 执行 git push
echo "执行 git push..."
git push || { echo "git push 执行失败"; exit 1; }

echo "脚本执行完毕：$(date)"

# 3. 用 macOS 的文本编辑器打开日志
open -a TextEdit "$LOG_FILE"