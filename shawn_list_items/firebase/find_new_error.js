


async function scrollAndExtractIssues() {
  let lastCount = 0;

  for (let i = 0; i < 20; i++) {
    window.scrollTo(0, document.body.scrollHeight);
    await new Promise(r => setTimeout(r, 1000)); // 等待页面加载

    const links = document.querySelectorAll('a.fire-router-link-host');
    if (links.length === lastCount) break;
    lastCount = links.length;
  }

  const issueIds = Array.from(document.querySelectorAll('a.fire-router-link-host'))
    .map(a => a.getAttribute('href'))
    .filter(href => href && href.includes('/issues/'))
    .map(href => href.match(/issues\/([^?]+)/)?.[1])
    .filter(Boolean);

  const unique = [...new Set(issueIds)];
  console.log('共抓取 issues 数：', unique.length);
  console.log(unique);
}

scrollAndExtractIssues();