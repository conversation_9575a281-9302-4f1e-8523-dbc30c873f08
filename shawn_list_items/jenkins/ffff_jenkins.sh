pipeline {
    agent any

    environment {
    // 客户端代码仓库
	app_git_url = "https://gitlab.ffff.team/client/aaron"
		
	// 客户脚本仓库	
	script_git_url = "https://gitlab.ffff.team/client/ffff-scripts.git"
		
	// aaron仓库分支号. 请更改它
    branches = "dev/1.2"
    }

    stages {
        stage('1.git checkout') {
            steps {
                // 清空本地所有文件    
    			deleteDir() 
    			
    			// 检出客户端仓库
    			dir("${JOB_NAME}/aaron"){
    				git branch: "${branches}",  credentialsId: 'jenkins', url: "$env.app_git_url" + ".git"
    				script {
    				  env.AUTHOR = sh (returnStdout: true, script: '#!/bin/sh -e\n git log -1 --pretty=%an').trim()
    				  env.GIT_COMMIT_ID = sh (returnStdout: true, script: "#!/bin/sh -e\n git rev-parse --short HEAD").trim()  
    				  // 获取changlog信息
                      env.changlog_path = '/var/lib/jenkins/jobs/${JOB_NAME}/builds/${BUILD_NUMBER}/changelog*.xml'
                      env.GIT_CHANGELOG = sh (returnStdout: true, script: "#!/bin/bash -e\n cat ${changlog_path} | grep '^[[:space:]]' |grep -v '#'| nl -w1 -s '.'").trim()
				    // sh "echo 变更记录: ${env.GIT_CHANGELOG }"
                    // sh "echo 变更id: ${env.GIT_COMMIT_ID }"
                    // sh "echo 变更作者: ${env.AUTHOR }"
                    }
    			}
                // 检出脚本仓库仓库
    			dir("${JOB_NAME}/script"){
    				git branch: "main",  credentialsId: 'jenkins', url: "$env.script_git_url"
    			}
    			
    		}
        }
        stage('2. command') {
			steps {
				dir("${JOB_NAME}/script/projectConfig"){
        			script {
        			    // 执行dart 脚本
        			    sh '''#!/bin/bash
        			    dart pub get
                        dart update_config.dart
                        '''
        			}
    			}
    		}
    	}
    	
    	
        stage('3 commit') {
            steps {
                dir("${JOB_NAME}"){
                    withCredentials([usernamePassword(credentialsId: 'jenkins', usernameVariable: 'user', passwordVariable: 'passwd')]) {
                        sh "rm -rf aaron-prod"
						
                        sh "git clone -b main https://$user:$<EMAIL>/client/aaron-prod"

                        sh "rm -rf aaron-prod/*"
                        
                        sh "cp -rf  aaron/* aaron-prod/"
                        dir("aaron-prod"){
                            sh """
                                git config user.name "${AUTHOR}"
                                git config user.email "jenkins.ffff.team"
                                git config --global push.default simple
                                git add .
                                git commit -m '"jenkins_job_id": ${BUILD_NUMBER},"git_change_log":"${GIT_CHANGELOG}","job_author": "${AUTHOR}", "jenkins_url": "${currentBuild.absoluteUrl}console", "git_url": "${env.app_git_url}/commit/${env.GIT_COMMIT_ID}"'
                                git push origin main
                            """
                        }
                        
                    }
                }
            }
        }
    }
}

