// Place your key bindings in this file to override the defaults
[   
    {
        "key": "shift+cmd+g",
        "command": "git-graph.view"
    },
    {
        "key": "shift+cmd+t",
        "command": "workbench.action.tasks.runTask"
    },
    {
        "key": "shift+cmd+r",
        "command": "revealFileInOS"
    },
    {
        "key": "alt+cmd+down",
        "command": "workbench.action.compareEditor.nextChange",
        "when": "textCompareEditorVisible"
    },
    {
        "key": "alt+f5",
        "command": "-workbench.action.compareEditor.nextChange",
        "when": "textCompareEditorVisible"
    },
    {
        "key": "alt+cmd+up",
        "command": "workbench.action.compareEditor.previousChange",
        "when": "textCompareEditorVisible"
    },
    {
        "key": "shift+alt+f5",
        "command": "-workbench.action.compareEditor.previousChange",
        "when": "textCompareEditorVisible"
    },
    {
        "key": "cmd+left",
        "command": "workbench.action.navigateBack",
        "when": "canNavigateBack"
    },
    {
        "key": "ctrl+-",
        "command": "-workbench.action.navigateBack",
        "when": "canNavigateBack"
    },
    {
        "key": "cmd+right",
        "command": "workbench.action.navigateForward",
        "when": "canNavigateForward"
    },
    {
        "key": "ctrl+shift+-",
        "command": "-workbench.action.navigateForward",
        "when": "canNavigateForward"
    },
    {
        "key": "ctrl+u",
        "command": "editor.action.transformToUppercase"
    },
    {
        "key": "ctrl+shift+u",
        "command": "editor.action.transformToLowercase"
    }
]