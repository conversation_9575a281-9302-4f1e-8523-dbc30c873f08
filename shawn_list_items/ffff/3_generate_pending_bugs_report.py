import os
import re

def read_crash_details(filepath):
    """
    从 crash 文件中读取完整的详细信息。

    Args:
        filepath (str): crash 文件路径。

    Returns:
        str or None: 文件的完整内容，如果读取失败则返回 None。
    """
    try:
        with open(filepath, 'r', encoding='utf-8') as f:
            return f.read()
    except Exception as e:
        print(f"Error reading file {filepath}: {e}")
        return None

def extract_issue_from_filename(filename):
    """
    从文件名中提取 Issue 值（假设文件名包含 Issue）。

    Args:
        filename (str): 文件名。

    Returns:
        str or None: 提取到的 Issue 值，如果没有找到则返回 None。
    """
    match = re.search(r"_([0-9a-fA-F]+)\.log$", filename)
    if match:
        return match.group(1)
    return None

def get_fix_info(fix_history_path, issue_to_find):
    """
    从 fix_history.txt 中获取指定 Issue 的负责人信息。

    Args:
        fix_history_path (str): fix_history.txt 文件的路径。
        issue_to_find (str): 要查找的 Issue 值。

    Returns:
        str or None: 负责人信息（例如 "负责人@沐北辰"），如果没有找到则返回 None。
    """
    try:
        with open(fix_history_path, 'r', encoding='utf-8') as f:
            content = f.read()
            for item in content.strip().split('\n\n'):
                issue_match = re.search(r"Issue: ([0-9a-fA-F]+)", item)
                if issue_match and issue_match.group(1) == issue_to_find:
                    responsible_match = re.search(r"负责人@(.+?)(?:\s|-)", item)
                    if responsible_match:
                        return f"负责人@{responsible_match.group(1).strip()}"
                    else:
                        responsible_match_at = re.search(r"@(.+?)(?:\s)", item)
                        if responsible_match_at:
                            return f"@{responsible_match_at.group(1).strip()}"
                        else:
                            return None
            return None
    except FileNotFoundError:
        print(f"Error: File '{fix_history_path}' not found.")
        return None
    except Exception as e:
        print(f"Error reading file '{fix_history_path}': {e}")
        return None

def generate_report(root_dir):
    """
    生成 bug 报告。

    Args:
        root_dir (str): 根目录路径。
    """
    fixed_dir = os.path.join(root_dir, ".crash", "1.2.0")
    unfixed_dir = os.path.join(root_dir, ".crash", "1.2.0_fixed")
    fix_history_path = os.path.join(root_dir, "4_feishu_fix_history.txt")
    fixed_report_path = os.path.join(root_dir, "5_crash_fixed.log")
    pending_report_path = os.path.join(root_dir, "5_crash_pending.log")

    resolved_bugs = {}
    unresolved_bugs = {}

    # 1. 读取已解决的 bug (从 ffff/.crash/1.2.0)
    if os.path.exists(fixed_dir):
        for filename in os.listdir(fixed_dir):
            if filename.endswith(".log"):
                issue = extract_issue_from_filename(filename)
                if issue:
                    filepath = os.path.join(fixed_dir, filename)
                    details = read_crash_details(filepath)
                    if details:
                        platform_match = re.search(r"# Platform: (.+)", details)
                        platform = platform_match.group(1).strip() if platform_match else "unknown"
                        resolved_bugs[issue] = {"details": details, "platform": platform.lower()}

    # 2. 读取未解决的 bug (从 ffff/.crash/1.2.0_fixed)
    if os.path.exists(unfixed_dir):
        for filename in os.listdir(unfixed_dir):
            if filename.endswith(".log"):
                match = re.match(r"([a-z]+)_([0-9a-fA-F]+)\.log", filename)
                if match:
                    platform = match.group(1)
                    issue = match.group(2)
                    filepath = os.path.join(unfixed_dir, filename)
                    details = read_crash_details(filepath)
                    if details:
                        unresolved_bugs[issue] = {"details": details, "platform": platform.lower()}

    # 生成已解决的 bug 报告
    fixed_report_content = ""
    # 对 resolved_bugs 按平台排序，android 在前，ios 在后
    sorted_resolved = sorted(resolved_bugs.items(), 
                           key=lambda x: (0 if 'android' in x[1]['platform'].lower() else 1, x[1]['platform']))

    for issue, data in sorted_resolved:
        fix_info = get_fix_info(fix_history_path, issue)
        if not fix_info:  # 如果没有找到负责人信息，跳过这个 bug
            continue
        fixed_report_content += f"{data['platform']} 1.2.0 {fix_info}\n"
        fixed_report_content += f"Issue: {issue}\n"
        fixed_report_content += data["details"].split('\n', 5)[4] + "\n"
        fixed_report_content += "\n"

    # 生成未解决的 bug 报告
    pending_report_content = ""
    # 对 unresolved_bugs 按平台排序，android 在前，ios 在后
    sorted_unresolved = sorted(unresolved_bugs.items(),
                             key=lambda x: (0 if 'android' in x[1]['platform'].lower() else 1, x[1]['platform']))

    for issue, data in sorted_unresolved:
        fix_info = get_fix_info(fix_history_path, issue)
        pending_report_content += f"{data['platform']} 1.2.0 {fix_info or ''}\n"
        pending_report_content += f"Issue: {issue}\n"
        pending_report_content += data["details"].split('\n', 5)[4] + "\n"
        pending_report_content += "\n"

    # 写入已解决的 bug 报告
    try:
        with open(fixed_report_path, 'w', encoding='utf-8') as f:
            f.write(fixed_report_content)
        print(f"已解决的 Bug 报告已生成: {fixed_report_path}")
    except Exception as e:
        print(f"Error writing fixed report to '{fixed_report_path}': {e}")

    # 写入未解决的 bug 报告
    try:
        with open(pending_report_path, 'w', encoding='utf-8') as f:
            f.write(pending_report_content)
        print(f"未解决的 Bug 报告已生成: {pending_report_path}")
    except Exception as e:
        print(f"Error writing pending report to '{pending_report_path}': {e}")

if __name__ == "__main__":
    root_directory = "ffff"  # 请将 "ffff" 替换为实际的根目录路径
    generate_report(root_directory)