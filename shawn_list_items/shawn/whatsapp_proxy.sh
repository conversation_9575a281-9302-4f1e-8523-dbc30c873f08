#!/bin/bash

# 检查代理服务器是否可用（尝试连接本地 SOCKS5 代理）
nc -z 127.0.0.1 7890
if [ $? -eq 0 ]; then
  echo "Proxy server is available, launching WhatsApp with proxy..."
else
  echo "Proxy server is not available, starting VPN..."
  /Users/<USER>/Projects/python/shawn_list_items/vpn/vpn_clash_x.sh
  sleep 3  # 等待几秒钟让 VPN 启动并建立连接
fi

# 启动 WhatsApp 并使用代理参数
open -a "WhatsApp" --args --proxy-server="socks5://127.0.0.1:7890"