#!/bin/bash

# 设置变量
AVD_NAME="Pixel_6"
VPN_SCRIPT="/Users/<USER>/Projects/python/shawn_list_items/vpn/vpn_clash_x.sh"
EMULATOR_PATH="/Users/<USER>/Library/Android/sdk/emulator/emulator"

# 可选：启动 VPN（如果你始终想要 VPN）
# echo "[INFO] Starting VPN..."
# $VPN_SCRIPT
# sleep 3

# 检查模拟器是否已启动
# 强制设置 adb 路径
ADB="/Users/<USER>/Library/Android/sdk/platform-tools/adb"
export PATH="/usr/local/bin:/opt/homebrew/bin:/Users/<USER>/Library/Android/sdk/platform-tools:$PATH"

# 查找模拟器进程
EMULATOR_PID=$(pgrep -f "emulator.*-avd $AVD_NAME")
if [ -n "$EMULATOR_PID" ]; then
  echo "[INFO] Emulator $AVD_NAME is already running. Stopping it..."
  SERIAL=$($ADB devices | grep emulator | awk '{print $1}')
  if [ -n "$SERIAL" ]; then
    $ADB -s "$SERIAL" emu kill
    sleep 3
  else
    echo "[WARN] Emulator is running but no adb device found."
  fi
fi

# 启动模拟器（尝试 QEMU 自定义网络）
echo "[INFO] Launching emulator $AVD_NAME with QEMU network settings..."
"$EMULATOR_PATH" -avd "$AVD_NAME" \
  -gpu host \
  -no-snapshot-load \
  -no-boot-anim \
  -noaudio \
  -netdelay none \
  -netspeed full \
  -qemu -net nic -net user,hostfwd=tcp::5555-:5555